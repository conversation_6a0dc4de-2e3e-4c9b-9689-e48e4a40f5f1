'use client';

import { Message } from "../types/types";
import { usePathname } from "next/navigation";
import Avatar from "./Avatar";
import { UserCircle } from "lucide-react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { useEffect, useRef, useMemo } from "react";

const SENDER_USER = 'user';

interface MessagesProps {
  messages: Message[];
  chatbotName: string;
  chatbotId: string;
}

const Messages: React.FC<MessagesProps> = ({ messages, chatbotName, chatbotId }) => {
  const ref = useRef<HTMLDivElement>(null);
  const path = usePathname();
  const isReviewPage = useMemo(() => path.includes("review-sessions"), [path]);

  useEffect(() => {
    if (ref.current) {
      ref.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  function getMessageClass(isUser: boolean, content: string | null) {
    return `whitespace-break-spaces mb-5 ${content === "Thinking..." && "animate-pulse"} ${!isUser ? "text-white" : "text-gray-700"}`;
  }

  if (!messages || messages.length === 0) {
    return (
      <div className="flex-1 flex flex-col overflow-y-auto space-y-10 py-10 px-5 bg-white rounded-lg">
        <div className="chat chat-start relative">
          <div className="chat-image avatar w-10">
            <Avatar
              seed={chatbotName}
              className="h-12 w-12 bg-white rounded-full border-2 border-[#2991EE]"
            />
          </div>
          <span className="chat-bubble text-white chat-bubble-primary bg-[#4D7DF8]">
            👋 Hi there! I'm {chatbotName}. How can I help you today?
          </span>
        </div>
      </div>
    );
  }

  return (
  <div className="flex-1 flex flex-col overflow-y-auto space-y-10 py-10 px-5 bg-white rounded-lg">{/* Removed close button for review sessions */}

    {messages.map((message) => {
      // Determine if this is a user message (should be on the right)
      const isUser = message.sender === SENDER_USER || message.role === 'user';
      // AI/Assistant messages go on the left, User messages go on the right
      return (
        <div key={message.id} className={`chat ${isUser ? "chat-end" : "chat-start"} relative`}>
          {isReviewPage && (
            <p className="absolute -bottom-5 text-xs text-gray-300" style={{ marginLeft: isUser ? '0px' : '10px' }}>
              sent {new Date(message.created_at).toLocaleString()}
            </p>
          )}
          <div className="chat-image avatar w-10">
            {!isUser ? (
              <Avatar
                seed={chatbotName}
                className="h-12 w-12 bg-white rounded-full border-2 border-[#2991EE]"
              />
            ) : (
              <UserCircle className="text-[#2991EE]" />
            )}
          </div>

          <span
            className={`chat-bubble ${!isUser ? "chat-bubble-primary bg-[#4D7DF8] text-white" : "chat-bubble-secondary bg-gray-200 text-gray-700"} max-w-xs md:max-w-md lg:max-w-lg xl:max-w-xl break-words`}
          >
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              className="break-words"
              components={{
                ul: ({ ...props }) => (
                  <ul {...props} className="list-disc list-inside ml-5 mb-5" />
                ),
                ol: ({ ...props }) => (
                  <ol {...props} className="list-decimal list-inside ml-5 mb-5" />
                ),
                h1: ({ ...props }) => (
                  <h1 {...props} className="text-2xl font-bold mb-5" />
                ),
                h2: ({ ...props }) => (
                  <h2 {...props} className="text-xl font-bold mb-5" />
                ),
                h3: ({ ...props }) => (
                  <h3 {...props} className="text-lg font-bold mb-5" />
                ),
                table: ({ ...props }) => (
                  <table {...props} className="table-auto w-full border-separate border-2 rounded-sm border-spacing-4 border-white mb-5" />
                ),
                th: ({ ...props }) => (
                  <th {...props} className="text-left underline" />
                ),
                p: ({ ...props }) => (
                  <p {...props} className={getMessageClass(isUser, message.content)} />
                ),
                a: ({ ...props }) => (
                  <a {...props} target="_blank" className="font-bold underline hover:text-blue-400" rel="noopener noreferrer" />
                ),
              }}
            >
              {message.content || ''}
            </ReactMarkdown>
          </span>
        </div>
      );
    })}

    <div ref={ref} />
  </div>
);

}
export default Messages;
