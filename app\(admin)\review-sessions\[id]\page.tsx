import React from 'react';
import Messages from "../../../../components/Messages";
import { createServerClient } from '@supabase/ssr';
import { cookies } from "next/headers";
import { Metadata } from 'next';

interface Message {
  id: number;
  content: string;
  role: 'user' | 'assistant' | 'system';
  created_at: string;
  sender?: string;
  chat_session_id: string;
}

interface Guest {
  id: number;
  name: string | null;
  email: string | null;
  created_at: string;
}

interface ChatSession {
  id: string;
  created_at: string;
  messages: Message[];
  chatbots: {
    name: string;
    id: string;
  };
  chatbot_id: string;
  guest_id: number | null;
  email: string | null;
  name: string | null;
  status: 'active' | 'closed' | 'archived';
  guest?: Guest | null;
}

// Forcing dynamic rendering (useful when data changes frequently or is dependent on state)
export const dynamic = "force-dynamic";

// The main Page component now receives `params` directly from Next.js
export default async function Page({
  params,
}: {
  params: Promise<{ id: string }>
}) {
  const { id } = await params;

  const cookieStore = await cookies();

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
      },
    }
  );

  // Fetch chat session with related data
  const { data: chatSession, error }: { data: ChatSession | null, error: any } = await supabase
    .from('chat_sessions')
    .select(`
      id,
      created_at,
      name,
      email,
      guest_id,
      status,
      chatbot_id,
      messages (
        id,
        content,
        role,
        created_at
      ),
      chatbots!chat_sessions_chatbot_id_fkey (
        name,
        id
      ),
      guest:guests (
        id,
        name,
        email,
        created_at
      )
    `)
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching chat session:', error);
    return (
      <div className="flex-1 p-10">
        <div className="bg-red-50 text-red-500 p-4 rounded-lg">
          {`Error loading chat session. (${error.message}) Please try again later.`}
        </div>
      </div>
    );
  }

  if (!chatSession) {
    return (
      <div className="flex-1 p-10">
        <div className="bg-yellow-50 text-yellow-600 p-4 rounded-lg">
          Chat session not found.
        </div>
      </div>
    );
  }

  

  // Handle guest information properly
  const guestInfo = chatSession.guest || {
    name: chatSession.name,
    email: chatSession.email,
    id: chatSession.guest_id
  };

  return (
    <div className="flex-1 p-10 pb-24">
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-xl lg:text-3xl font-semibold">Session Review</h1>
            <p className="font-light text-gray-400 mt-2">
              Started at {new Date(chatSession.created_at).toLocaleString()}
            </p>
          </div>
          <div className={`px-3 py-1 rounded-full text-sm ${chatSession.status === 'active' ? 'bg-green-100 text-green-800' : chatSession.status === 'closed' ? 'bg-gray-100 text-gray-800' : 'bg-yellow-100 text-yellow-800'}`}>
            {chatSession.status.charAt(0).toUpperCase() + chatSession.status.slice(1)}
          </div>
        </div>

        <div className="mt-6">
          <h2 className="text-lg font-medium mb-2">Participants</h2>

          <div className="flex items-center mb-4">
            <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
              Bot: {chatSession.chatbots?.name || 'Unknown'}
            </div>
          </div>

          <div className="space-y-2">
            {(guestInfo.name || guestInfo.email) ? (
              <div className="flex items-center space-x-4 bg-gray-50 p-3 rounded-lg">
                <div className="bg-gray-200 p-2 rounded-full">
                  {guestInfo.name ? guestInfo.name[0].toUpperCase() : 'G'}
                </div>
                <div>
                  {guestInfo.name && (
                    <p className="font-medium">{guestInfo.name}</p>
                  )}
                  {guestInfo.email && (
                    <p className="text-sm text-gray-500">{guestInfo.email}</p>
                  )}
                  {chatSession.guest_id && (
                    <p className="text-xs text-gray-400 mt-1">
                      Registered Guest #{chatSession.guest_id}
                    </p>
                  )}
                </div>
              </div>
            ) : (
              <p className="text-gray-500 italic">Anonymous user</p>
            )}
          </div>
        </div>

        <hr className="my-6" />

        <div className="mt-6">
          <h2 className="text-lg font-medium mb-4">Chat History</h2>
          {chatSession.messages.length > 0 ? (
            <Messages
              messages={chatSession.messages.map(msg => ({
                ...msg,
                id: Number(msg.id),
                sender: msg.role === 'assistant' ? (chatSession.chatbots?.name || 'Assistant') : 'user',
                chat_session_id: Number(chatSession.id),
                content: msg.content || '',
                role: msg.role as 'user' | 'assistant' | 'system'
              }))}
              chatbotName={chatSession.chatbots?.name || 'Assistant'}
              chatbotId={chatSession.chatbot_id}
            />
          ) : (
            <p className="text-gray-500 italic">No messages found for this session.</p>
          )}
        </div>
      </div>
    </div>
  );
}

// Generate Metadata
export async function generateMetadata({
  params,
}: {
  params: Promise<{ id: string }>;
}): Promise<Metadata> {
  const { id } = await params;
  return {
    title: `Review Session ${id}`,
    description: `Review chat session ${id} details and conversation history`,
  };
}
